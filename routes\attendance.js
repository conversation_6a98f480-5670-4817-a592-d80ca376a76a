const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const Attendance = require('../models/Attendance');
const ClassSession = require('../models/ClassSession');
const Student = require('../models/Student');
const { requireAuth } = require('../middleware/auth');

/**
 * GET /attendance/scanner - QR Code Scanner Interface
 */
router.get('/scanner', requireAuth, async (req, res) => {
    try {
        // Get active sessions for the current teacher
        const activeSessions = await ClassSession.findByTeacher(req.user.user_id, {
            status: 'active',
            includeSubject: true
        });

        res.render('attendance/scanner', {
            title: 'QR Code Scanner',
            user: req.user,
            activeSessions: activeSessions,
            selectedSessionId: req.query.session_id || null
        });
    } catch (error) {
        console.error('Error loading scanner page:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load scanner page',
            error: error
        });
    }
});

/**
 * POST /attendance/scan - Record attendance via QR code scan
 */
router.post('/scan', requireAuth, [
    body('student_qr_data')
        .notEmpty()
        .withMessage('Student QR code data is required'),
    body('session_qr_data')
        .notEmpty()
        .withMessage('Session QR code data is required')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { student_qr_data, session_qr_data } = req.body;
        const clientIP = req.ip || req.connection.remoteAddress;

        const result = await Attendance.recordViaScan({
            studentQRData: student_qr_data,
            sessionQRData: session_qr_data,
            teacherId: req.user.user_id,
            clientIP
        });

        if (result.success) {
            return res.status(201).json(result);
        } else {
            return res.status(400).json(result);
        }
    } catch (error) {
        console.error('Error recording attendance:', error);
        return res.status(500).json({
            success: false,
            error: 'Failed to record attendance',
            details: error.message
        });
    }
});

/**
 * GET /attendance/session/:sessionId - Get attendance for a session
 */
router.get('/session/:sessionId', requireAuth, async (req, res) => {
    try {
        const sessionId = parseInt(req.params.sessionId);
        
        // Verify session exists
        const session = await ClassSession.findById(sessionId);
        if (!session) {
            return res.status(404).json({
                success: false,
                message: 'Session not found'
            });
        }

        const attendance = await Attendance.findBySession(sessionId);
        const stats = await Attendance.getSessionStats(sessionId);

        if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
            return res.json({
                success: true,
                session: session.toJSON(),
                attendance: attendance.map(record => record.toJSON()),
                statistics: stats
            });
        }

        res.render('attendance/session', {
            title: `Attendance - ${session.subject_name}`,
            session,
            attendance,
            statistics: stats,
            success: req.session.success,
            error: req.session.error
        });

        delete req.session.success;
        delete req.session.error;
    } catch (error) {
        console.error('Error fetching session attendance:', error);
        
        if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
            return res.status(500).json({
                success: false,
                message: 'Failed to fetch attendance'
            });
        }

        req.session.error = 'Failed to fetch attendance';
        res.redirect('/sessions');
    }
});

/**
 * GET /attendance/student/:studentId - Get attendance history for a student
 */
router.get('/student/:studentId', requireAuth, async (req, res) => {
    try {
        const studentId = parseInt(req.params.studentId);
        
        // Verify student exists
        const student = await Student.findById(studentId);
        if (!student) {
            return res.status(404).json({
                success: false,
                message: 'Student not found'
            });
        }

        const attendance = await Attendance.findByStudent(studentId);
        const stats = await Attendance.getStudentStats(studentId);

        if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
            return res.json({
                success: true,
                student: student.toJSON(),
                attendance: attendance.map(record => record.toJSON()),
                statistics: stats
            });
        }

        res.render('attendance/student', {
            title: `Attendance - ${student.getFullName()}`,
            student,
            attendance,
            statistics: stats,
            success: req.session.success,
            error: req.session.error
        });

        delete req.session.success;
        delete req.session.error;
    } catch (error) {
        console.error('Error fetching student attendance:', error);
        
        if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
            return res.status(500).json({
                success: false,
                message: 'Failed to fetch attendance'
            });
        }

        req.session.error = 'Failed to fetch attendance';
        res.redirect('/students');
    }
});

/**
 * GET /attendance/today - Get today's attendance
 */
router.get('/today', requireAuth, async (req, res) => {
    try {
        const attendance = await Attendance.findToday();

        return res.json({
            success: true,
            attendance: attendance.map(record => record.toJSON())
        });
    } catch (error) {
        console.error('Error fetching today\'s attendance:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to fetch today\'s attendance'
        });
    }
});

/**
 * GET /attendance - List all attendance records with filters
 */
router.get('/', requireAuth, async (req, res) => {
    try {
        const filters = {};
        
        // Apply filters from query parameters
        if (req.query.student_id) {
            filters.student_id = parseInt(req.query.student_id);
        }
        
        if (req.query.session_id) {
            filters.session_id = parseInt(req.query.session_id);
        }

        if (req.query.teacher_id) {
            filters.teacher_id = parseInt(req.query.teacher_id);
        }

        if (req.query.status) {
            filters.status = req.query.status;
        }

        if (req.query.date_from) {
            filters.date_from = req.query.date_from;
        }

        if (req.query.date_to) {
            filters.date_to = req.query.date_to;
        }

        const attendance = await Attendance.findAll(filters);

        if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
            return res.json({
                success: true,
                attendance: attendance.map(record => record.toJSON())
            });
        }

        res.render('attendance/index', {
            title: 'Attendance Records',
            attendance,
            filters: req.query,
            success: req.session.success,
            error: req.session.error
        });

        delete req.session.success;
        delete req.session.error;
    } catch (error) {
        console.error('Error fetching attendance:', error);
        
        if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
            return res.status(500).json({
                success: false,
                message: 'Failed to fetch attendance'
            });
        }

        req.session.error = 'Failed to fetch attendance';
        res.redirect('/dashboard');
    }
});

/**
 * GET /attendance/test-qr - Generate test QR codes for testing
 */
router.get('/test-qr', requireAuth, async (req, res) => {
    try {
        // Get some sample students and sessions for testing
        const Student = require('../models/Student');
        const ClassSession = require('../models/ClassSession');

        const students = await Student.findAll({ limit: 5 });
        const sessions = await ClassSession.findByTeacher(req.user.user_id, { limit: 3 });

        res.render('attendance/test-qr', {
            title: 'Test QR Codes',
            user: req.user,
            students: students,
            sessions: sessions
        });
    } catch (error) {
        console.error('Error loading test QR page:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load test QR page',
            error: error
        });
    }
});

module.exports = router;
